#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحويل محسن للصور إلى فيديو مع نص عربي صحيح
Improved image to video converter with proper Arabic text
"""

import moviepy.editor as mp
from moviepy.editor import ImageSequenceClip, AudioFileClip
import os
import shutil
from improved_arabic_presentation import create_improved_presentation

def create_video_from_improved_images(images, output_file, duration_per_slide=5):
    """إنشاء فيديو من الصور المحسنة"""
    try:
        if not images:
            print("لا توجد صور لإنشاء الفيديو")
            return False
        
        print(f"إنشاء فيديو من {len(images)} صورة محسنة...")
        
        # إنشاء مقاطع فيديو من الصور
        clips = []
        for i, img_path in enumerate(images):
            print(f"معالجة الصورة {i+1}/{len(images)}")
            
            # إنشاء مقطع فيديو من الصورة لمدة محددة
            clip = mp.ImageClip(img_path, duration=duration_per_slide)
            
            # إضافة تأثير انتقال ناعم
            if i > 0:
                clip = clip.crossfadein(0.5)
            if i < len(images) - 1:
                clip = clip.crossfadeout(0.5)
            
            clips.append(clip)
        
        # دمج جميع المقاطع
        print("دمج المقاطع...")
        final_video = mp.concatenate_videoclips(clips, method="compose")
        
        # تعيين معدل الإطارات
        final_video = final_video.set_fps(30)
        
        # إضافة معلومات الفيديو
        total_duration = len(images) * duration_per_slide
        print(f"مدة الفيديو الإجمالية: {total_duration} ثانية")
        print(f"عدد الشرائح: {len(images)}")
        print(f"مدة كل شريحة: {duration_per_slide} ثوانٍ")
        
        # كتابة الفيديو
        print("كتابة الفيديو...")
        final_video.write_videofile(
            output_file,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None,
            preset='medium',
            ffmpeg_params=['-crf', '18']  # جودة عالية
        )
        
        print(f"تم إنشاء الفيديو بنجاح: {output_file}")
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء الفيديو: {e}")
        return False

def create_complete_project():
    """إنشاء المشروع الكامل مع النص العربي المحسن"""
    
    print("=" * 60)
    print("مشروع جفاف مياه دجلة والفرات - النسخة المحسنة")
    print("Tigris and Euphrates Drought Project - Improved Version")
    print("=" * 60)
    
    # إنشاء الشرائح المحسنة
    print("\n🎨 إنشاء الشرائح مع النص العربي المحسن...")
    images, temp_dir = create_improved_presentation()
    
    if not images:
        print("❌ فشل في إنشاء الشرائح")
        return False
    
    # إنشاء الفيديو
    print("\n🎬 تحويل الشرائح إلى فيديو...")
    video_file = "جفاف_دجلة_والفرات_فيديو_محسن.mp4"
    success = create_video_from_improved_images(images, video_file, duration_per_slide=5)
    
    # تنظيف الملفات المؤقتة
    if temp_dir and os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
        print("🧹 تم حذف الملفات المؤقتة")
    
    if success:
        print("\n" + "=" * 60)
        print("✅ تم إكمال المشروع بنجاح!")
        print("=" * 60)
        print(f"📁 الفيديو المحسن: {video_file}")
        print("🎯 المميزات الجديدة:")
        print("   • نص عربي صحيح من اليمين إلى اليسار")
        print("   • خطوط عربية عالية الجودة")
        print("   • ظلال للنص لتحسين الوضوح")
        print("   • إطارات زخرفية")
        print("   • انتقالات ناعمة بين الشرائح")
        print("   • جودة فيديو محسنة")
        print("=" * 60)
        return True
    else:
        print("❌ فشل في إنشاء الفيديو")
        return False

def display_project_info():
    """عرض معلومات المشروع"""
    print("\n📋 معلومات المشروع:")
    print("-" * 40)
    print("🎯 الهدف: عرض تقديمي حول جفاف مياه دجلة والفرات")
    print("📊 عدد الشرائح: 12 شريحة")
    print("⏱️ مدة الفيديو: 60 ثانية")
    print("🎨 الدقة: 1920x1080 (Full HD)")
    print("🔤 النص: عربي محسن من اليمين إلى اليسار")
    print("🎬 التنسيق: MP4 عالي الجودة")
    print("✨ المميزات الجديدة:")
    print("   - معالجة صحيحة للنص العربي")
    print("   - خطوط عربية واضحة")
    print("   - ظلال وتأثيرات بصرية")
    print("   - انتقالات ناعمة")
    print("   - جودة محسنة")

if __name__ == "__main__":
    display_project_info()
    create_complete_project()
