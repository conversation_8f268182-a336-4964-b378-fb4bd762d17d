#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تقديمي محسن مع دعم أفضل للنص العربي
Improved presentation with better Arabic text support
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from PIL import Image, ImageDraw, ImageFont
from bidi.algorithm import get_display
import arabic_reshaper
import os
import tempfile
import shutil

def process_arabic_text(text):
    """معالجة النص العربي للعرض الصحيح من اليمين إلى اليسار"""
    try:
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        # تطبيق خوارزمية الاتجاه الثنائي
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except:
        # في حالة فشل المعالجة، إرجاع النص الأصلي
        return text

def create_slide_image(slide_data, slide_number, output_dir):
    """إنشاء صورة للشريحة مع نص عربي محسن"""
    
    # إنشاء صورة فارغة بحجم 1920x1080
    img = Image.new('RGB', (1920, 1080), color=slide_data["bg_color"])
    draw = ImageDraw.Draw(img)
    
    # محاولة تحميل خطوط عربية عالية الجودة
    font_paths = [
        "C:/Windows/Fonts/arial.ttf",
        "C:/Windows/Fonts/tahoma.ttf",
        "C:/Windows/Fonts/calibri.ttf",
        "C:/Windows/Fonts/segoeui.ttf",
        "arial.ttf",
        "tahoma.ttf"
    ]
    
    font_title = None
    font_content = None
    font_subtitle = None
    
    for font_path in font_paths:
        try:
            font_title = ImageFont.truetype(font_path, 70)
            font_content = ImageFont.truetype(font_path, 36)
            font_subtitle = ImageFont.truetype(font_path, 50)
            print(f"تم تحميل الخط: {font_path}")
            break
        except:
            continue
    
    if font_title is None:
        font_title = ImageFont.load_default()
        font_content = ImageFont.load_default()
        font_subtitle = ImageFont.load_default()
        print("تم استخدام الخط الافتراضي")
    
    # رسم العنوان مع معالجة النص العربي
    title = process_arabic_text(slide_data["title"])
    
    # إضافة ظل للنص
    shadow_offset = 3
    bbox = draw.textbbox((0, 0), title, font=font_title)
    text_width = bbox[2] - bbox[0]
    x = (1920 - text_width) // 2
    y = 80
    
    # رسم الظل
    draw.text((x + shadow_offset, y + shadow_offset), title, fill=(0, 0, 0, 128), font=font_title)
    # رسم النص الأساسي
    draw.text((x, y), title, fill=slide_data["text_color"], font=font_title)
    
    # رسم العنوان الفرعي أو المحتوى
    if "subtitle" in slide_data:
        subtitle = process_arabic_text(slide_data["subtitle"])
        bbox_sub = draw.textbbox((0, 0), subtitle, font=font_subtitle)
        sub_width = bbox_sub[2] - bbox_sub[0]
        x_sub = (1920 - sub_width) // 2
        y_sub = 300
        
        # رسم ظل العنوان الفرعي
        draw.text((x_sub + shadow_offset, y_sub + shadow_offset), subtitle, fill=(0, 0, 0, 128), font=font_subtitle)
        draw.text((x_sub, y_sub), subtitle, fill=slide_data["subtitle_color"], font=font_subtitle)
    
    elif "content" in slide_data:
        content_lines = slide_data["content"]
        y_start = 220
        line_height = 70
        
        for j, line in enumerate(content_lines):
            if line.strip():  # تجاهل الأسطر الفارغة
                processed_line = process_arabic_text(line)
                y_pos = y_start + (j * line_height)
                
                # محاذاة النص من اليمين للعربي مع هامش أكبر
                bbox_line = draw.textbbox((0, 0), processed_line, font=font_content)
                line_width = bbox_line[2] - bbox_line[0]
                x_pos = 1800 - line_width  # محاذاة من اليمين مع هامش
                
                # رسم ظل النص
                draw.text((x_pos + 2, y_pos + 2), processed_line, fill=(0, 0, 0, 100), font=font_content)
                # رسم النص الأساسي
                draw.text((x_pos, y_pos), processed_line, fill=slide_data["content_color"], font=font_content)
    
    # إضافة إطار زخرفي
    border_color = (100, 100, 100)
    border_width = 5
    draw.rectangle([border_width, border_width, 1920-border_width, 1080-border_width], 
                  outline=border_color, width=border_width)
    
    # حفظ الصورة
    img_path = os.path.join(output_dir, f"slide_{slide_number:02d}.png")
    img.save(img_path, quality=95, optimize=True)
    print(f"تم إنشاء الشريحة {slide_number}: {slide_data['title']}")
    
    return img_path

def create_improved_presentation():
    """إنشاء عرض تقديمي محسن مع صور عالية الجودة"""
    
    # بيانات الشرائح
    slides_data = [
        {
            "title": "جفاف مياه دجلة والفرات في العراق",
            "subtitle": "أزمة المياه في بلاد الرافدين",
            "bg_color": (0, 51, 102),
            "text_color": (255, 255, 255),
            "subtitle_color": (218, 165, 32)
        },
        {
            "title": "بلاد الرافدين: مهد الحضارة",
            "content": [
                "• نهرا دجلة والفرات: شريان الحياة في العراق منذ آلاف السنين",
                "• مهد الحضارات القديمة: بابل وآشور وسومر",
                "• مصدر المياه الرئيسي للزراعة والشرب والصناعة",
                "• يغذي النهران أكثر من 40 مليون نسمة في المنطقة",
                "• العراق يعتمد على هذين النهرين بنسبة 98% من موارده المائية"
            ],
            "bg_color": (245, 245, 245),
            "text_color": (0, 51, 102),
            "content_color": (51, 51, 51)
        },
        {
            "title": "الوضع الحالي: أزمة مياه حادة",
            "content": [
                "• انخفاض منسوب المياه بنسبة 29% في دجلة و73% في الفرات",
                "• تحول أجزاء من النهرين إلى برك من مياه الصرف الصحي",
                "• تهديد بجفاف كامل للنهرين بحلول عام 2040",
                "• تأثر 7 ملايين عراقي بنقص المياه الصالحة للشرب",
                "• انخفاض الإنتاج الزراعي بنسبة تزيد عن 50%"
            ],
            "bg_color": (255, 240, 240),
            "text_color": (139, 0, 0),
            "content_color": (139, 0, 0)
        },
        {
            "title": "الأسباب الخارجية للأزمة",
            "content": [
                "• السدود التركية: 22 سداً على نهري دجلة والفرات",
                "• سد أليسو التركي: يحجب 50% من مياه دجلة",
                "• السدود الإيرانية: تحويل مجرى روافد دجلة",
                "• قطع إيران لنهري الزاب الصغير وسيروان",
                "• عدم التزام الدول المجاورة بالاتفاقيات المائية الدولية"
            ],
            "bg_color": (240, 248, 255),
            "text_color": (0, 51, 102),
            "content_color": (51, 51, 51)
        },
        {
            "title": "الأسباب الداخلية والمناخية",
            "content": [
                "• التغير المناخي: ارتفاع درجات الحرارة وقلة الأمطار",
                "• موسم 2020-2021: ثاني أكثر المواسم جفافاً منذ 40 عاماً",
                "• سوء إدارة الموارد المائية المحلية",
                "• تلوث المياه بسبب مياه الصرف الصحي والنفايات الصناعية",
                "• الاستخدام المفرط للمياه الجوفية"
            ],
            "bg_color": (255, 248, 220),
            "text_color": (184, 134, 11),
            "content_color": (139, 69, 19)
        },
        {
            "title": "التأثير على القطاع الزراعي",
            "content": [
                "• تراجع المساحات المزروعة بنسبة 70%",
                "• انخفاض إنتاج القمح والشعير والأرز",
                "• هجرة المزارعين من الريف إلى المدن",
                "• تدهور الأراضي الزراعية وتصحرها",
                "• فقدان العراق لاكتفائه الذاتي من المحاصيل الأساسية"
            ],
            "bg_color": (240, 255, 240),
            "text_color": (34, 139, 34),
            "content_color": (0, 100, 0)
        },
        {
            "title": "التأثيرات الاجتماعية والاقتصادية",
            "content": [
                "• نزوح أكثر من 400 ألف شخص من المناطق المتضررة",
                "• ارتفاع معدلات البطالة في القطاع الزراعي",
                "• تدهور الأوضاع الصحية بسبب تلوث المياه",
                "• زيادة النزاعات المحلية حول الموارد المائية المتبقية",
                "• تهديد الأمن الغذائي والاستقرار الاجتماعي"
            ],
            "bg_color": (255, 245, 238),
            "text_color": (205, 92, 92),
            "content_color": (139, 69, 19)
        },
        {
            "title": "تأثير على قطاع الكهرباء",
            "content": [
                "• انخفاض إنتاج الكهرباء من السدود المائية",
                "• توقف عدة محطات توليد كهرومائية عن العمل",
                "• زيادة الاعتماد على الوقود الأحفوري المكلف",
                "• انقطاع متكرر للتيار الكهربائي في المناطق المتضررة",
                "• تأثير سلبي على الصناعات التي تعتمد على الكهرباء"
            ],
            "bg_color": (255, 255, 224),
            "text_color": (255, 140, 0),
            "content_color": (255, 69, 0)
        },
        {
            "title": "الحلول والمقترحات",
            "content": [
                "• التفاوض مع تركيا وإيران لضمان حصة عادلة من المياه",
                "• تطبيق الاتفاقيات المائية الدولية",
                "• تحسين كفاءة استخدام المياه في الزراعة",
                "• تطوير تقنيات الري الحديثة والذكية",
                "• معالجة مياه الصرف الصحي وإعادة استخدامها"
            ],
            "bg_color": (240, 255, 240),
            "text_color": (34, 139, 34),
            "content_color": (0, 100, 0)
        },
        {
            "title": "الحلول التقنية والبديلة",
            "content": [
                "• تحلية مياه البحر من الخليج العربي",
                "• حفر آبار جوفية عميقة بتقنيات حديثة",
                "• بناء سدود وخزانات مائية محلية",
                "• استخدام تقنيات الذكاء الاصطناعي لإدارة المياه",
                "• تطوير مصادر طاقة متجددة لتشغيل محطات التحلية"
            ],
            "bg_color": (230, 244, 255),
            "text_color": (0, 51, 102),
            "content_color": (25, 25, 112)
        },
        {
            "title": "دور المجتمع الدولي",
            "content": [
                "• الضغط الدولي على تركيا وإيران لاحترام حقوق المياه",
                "• تقديم المساعدات التقنية والمالية للعراق",
                "• دعم مشاريع البنية التحتية المائية",
                "• تطبيق القانون الدولي للمياه العابرة للحدود",
                "• التعاون في مواجهة تحديات التغير المناخي"
            ],
            "bg_color": (248, 248, 255),
            "text_color": (75, 0, 130),
            "content_color": (72, 61, 139)
        },
        {
            "title": "الخاتمة: إنقاذ بلاد الرافدين",
            "content": [
                "أزمة جفاف دجلة والفرات تهدد مستقبل العراق وحضارته العريقة",
                "",
                "الحل يتطلب تضافر الجهود المحلية والإقليمية والدولية لضمان:",
                "• حق العراق في حصة عادلة من مياه النهرين",
                "• تطوير بدائل مائية مستدامة",
                "• حماية البيئة والتراث الحضاري لبلاد الرافدين",
                "",
                "المياه حق إنساني أساسي وليس سلاحاً سياسياً"
            ],
            "bg_color": (255, 248, 220),
            "text_color": (184, 134, 11),
            "content_color": (139, 69, 19)
        }
    ]
    
    # إنشاء مجلد مؤقت للصور
    temp_dir = tempfile.mkdtemp()
    images = []
    
    print("بدء إنشاء الشرائح المحسنة...")
    
    # إنشاء صور الشرائح
    for i, slide_data in enumerate(slides_data):
        img_path = create_slide_image(slide_data, i + 1, temp_dir)
        images.append(img_path)
    
    print(f"تم إنشاء {len(images)} شريحة بنجاح!")
    
    return images, temp_dir

if __name__ == "__main__":
    images, temp_dir = create_improved_presentation()
    print(f"تم حفظ الصور في: {temp_dir}")
    print("يمكنك الآن استخدام هذه الصور لإنشاء الفيديو")
